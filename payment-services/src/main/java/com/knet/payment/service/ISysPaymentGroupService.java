package com.knet.payment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.payment.model.dto.resp.OrderPaymentInfoResponse;
import com.knet.payment.model.entity.SysPaymentGroup;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-03-12 13:37:21
 * @description: 针对表【sys_payment_group(支付组主表)】的数据库操作Service
 */
public interface ISysPaymentGroupService extends IService<SysPaymentGroup> {

    /**
     * 更新支付组状态
     *
     * @param groupId    支付组ID
     * @param paidAmount 已支付金额
     */
    void updatePaymentGroupStatus(String groupId, BigDecimal paidAmount);

    /**
     * 根据订单ID获取支付信息
     *
     * @param orderId 订单ID
     * @return 支付信息
     */
    OrderPaymentInfoResponse getPaymentInfoByOrderId(String orderId);

    /**
     * 根据支付组ID获取支付组
     *
     * @param groupId 支付组ID
     * @return 支付组
     */
    SysPaymentGroup getByGroupId(String groupId);

    /**
     * 根据订单ID获取支付组
     *
     * @param orderId 订单ID
     * @return 支付组
     */
    SysPaymentGroup getByGroupIdByOrderId(String orderId);
}
