package com.knet.payment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.KnetPaymentGroupStatus;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.common.utils.RandomStrUtil;
import com.knet.payment.mapper.SysPaymentGroupMapper;
import com.knet.payment.model.dto.resp.OrderPaymentInfoResponse;
import com.knet.payment.model.entity.SysPaymentGroup;
import com.knet.payment.service.ISysPaymentGroupService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-03-12 13:37:21
 * @description: 针对表【sys_payment_group(支付组主表)】的数据库操作Service实现
 */
@Service
public class SysPaymentGroupServiceImpl extends ServiceImpl<SysPaymentGroupMapper, SysPaymentGroup> implements ISysPaymentGroupService {
    @Resource
    private RandomStrUtil randomStrUtil;

    /**
     * 更新支付组状态
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePaymentGroupStatus(String groupId, BigDecimal paidAmount) {
        LambdaQueryWrapper<SysPaymentGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysPaymentGroup::getGroupId, groupId);
        SysPaymentGroup paymentGroup = this.getOne(queryWrapper);
        if (paymentGroup == null) {
            throw new RuntimeException("支付组不存在");
        }
        BigDecimal newPaidAmount = paymentGroup.getPaidAmount().add(paidAmount);
        paymentGroup.setPaidAmount(newPaidAmount);
        // 如果已支付金额等于总金额，则标记为已完成
        if (newPaidAmount.compareTo(paymentGroup.getTotalAmount()) >= 0) {
            paymentGroup.setStatus(KnetPaymentGroupStatus.COMPLETED);
        }
        this.updateById(paymentGroup);
    }

    @Override
    public OrderPaymentInfoResponse getPaymentInfoByOrderId(String orderId) {
        LambdaQueryWrapper<SysPaymentGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysPaymentGroup::getOrderId, orderId);
        SysPaymentGroup paymentGroup = this.getOne(queryWrapper);
        if (paymentGroup == null) {
            return null;
        }
        return OrderPaymentInfoResponse.builder()
                .groupId(paymentGroup.getGroupId())
                .orderId(paymentGroup.getOrderId())
                .totalAmount(PriceFormatUtil.formatPrice(paymentGroup.getTotalAmount()))
                .paidAmount(PriceFormatUtil.formatPrice(paymentGroup.getPaidAmount()))
                .status(paymentGroup.getStatus().name())
                .paymentTime(paymentGroup.getUpdateTime())
                .createTime(paymentGroup.getCreateTime())
                .updateTime(paymentGroup.getUpdateTime())
                .build();
    }

    @Override
    public SysPaymentGroup getByGroupId(String groupId) {
        LambdaQueryWrapper<SysPaymentGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysPaymentGroup::getGroupId, groupId);
        return this.getOne(queryWrapper);
    }

    @Override
    public SysPaymentGroup getByGroupIdByOrderId(String orderId) {
        LambdaQueryWrapper<SysPaymentGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysPaymentGroup::getOrderId, orderId);
        return this.getOne(queryWrapper);
    }
}
