package com.knet.payment.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.knet.common.enums.KnetPaymentGroupStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.payment.service.ISysPaymentGroupService;
import com.knet.payment.service.ISysPaymentFlowService;
import com.knet.payment.system.event.InventoryFailedEvent;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;

import static com.knet.common.constants.SystemConstant.PAYMENT_INVENTORY_PROCESSED;
import static com.knet.common.constants.SystemConstant.PROCESSED;

/**
 * <AUTHOR>
 * @date 2025/6/11 16:15
 * @description: 库存扣减失败补偿事件消费者
 */
@Slf4j
@Component
public class InventoryFailedConsumer {
    
    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private ISysPaymentGroupService paymentGroupService;
    @Resource
    private ISysPaymentFlowService paymentFlowService;

    @RabbitListener(
            queues = "inventory-compensation-queue.payment-services",
            ackMode = "MANUAL"
    )
    public void handleInventoryFailed(
            @Payload String messageBody,
            @Header("routingKey") String routingKey,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            if ("inventory.failed".equals(routingKey)) {
                // 幂等性检查
                if (!redisCacheUtil.setIfAbsent(PAYMENT_INVENTORY_PROCESSED.formatted(messageId), PROCESSED, 60)) {
                    log.warn("payment服务重复消息: {}", messageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                processInventoryFailed(messageBody);
                channel.basicAck(deliveryTag, false);
            }
        } catch (Exception e) {
            log.error("库存扣减失败补偿处理失败: {}", messageBody, e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("消息拒绝失败: {}", ex.getMessage());
            }
        }
    }

    /**
     * 处理库存扣减失败补偿
     *
     * @param messageBody 消息体
     */
    @Transactional(rollbackFor = Exception.class)
    private void processInventoryFailed(String messageBody) {
        InventoryFailedEvent failedEvent = JSON.parseObject(messageBody, InventoryFailedEvent.class);
        log.info("支付服务处理库存扣减失败补偿: {}", messageBody);
        
        try {
            String orderId = failedEvent.getOrderId();
            
            // 1. 查询支付组信息
            var paymentInfo = paymentGroupService.getPaymentInfoByOrderId(orderId);
            if (paymentInfo == null) {
                log.warn("支付组不存在，无需补偿: orderId={}", orderId);
                return;
            }
            
            // 2. 更新支付组状态为已关闭
            var paymentGroup = paymentGroupService.getByGroupId(paymentInfo.getGroupId());
            if (paymentGroup != null && !KnetPaymentGroupStatus.CLOSED.equals(paymentGroup.getStatus())) {
                paymentGroup.setStatus(KnetPaymentGroupStatus.CLOSED);
                paymentGroupService.updateById(paymentGroup);
                log.info("支付组状态已更新为已关闭: groupId={}", paymentInfo.getGroupId());
            }
            
            // 3. 逻辑删除相关支付流水（设置deleted字段为1）
            // 这里假设BaseEntity有deleted字段，如果没有可以添加状态字段
            // 由于支付流水可能有多条，需要批量更新
            // paymentFlowService.logicalDeleteByGroupId(paymentInfo.getGroupId());
            
            log.info("库存扣减失败补偿处理完成: orderId={}, reason={}", orderId, failedEvent.getFailureReason());
            
        } catch (Exception e) {
            log.error("处理库存扣减失败补偿异常: orderId={}, error={}", failedEvent.getOrderId(), e.getMessage());
            throw new ServiceException("处理库存扣减失败补偿异常: " + e.getMessage());
        }
    }
}
