package com.knet.goods.system.event;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/11 16:00
 * @description: 库存扣减失败事件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryFailedEvent implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORD-123456789012345678")
    private String orderId;
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;
    @Schema(description = "失败原因")
    private String failureReason;
    @Schema(description = "失败时间戳")
    private Long failedTime;
    @Schema(description = "订单总金额")
    private String totalAmount;

    public static InventoryFailedEvent create(String orderId, Long userId, String failureReason, String totalAmount) {
        return InventoryFailedEvent.builder()
                .orderId(orderId)
                .userId(userId)
                .failureReason(failureReason)
                .totalAmount(totalAmount)
                .failedTime(System.currentTimeMillis())
                .build();
    }
}
