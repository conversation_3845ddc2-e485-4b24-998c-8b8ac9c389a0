package com.knet.order.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderService;
import com.knet.order.system.event.InventoryFailedEvent;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;

import static com.knet.common.constants.SystemConstant.ORDER_INVENTORY_PROCESSED;
import static com.knet.common.constants.SystemConstant.PROCESSED;

/**
 * <AUTHOR>
 * @date 2025/6/11 16:25
 * @description: 库存扣减失败补偿事件消费者
 */
@Slf4j
@Component
public class InventoryFailedConsumer {
    
    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private ISysOrderGroupService orderGroupService;
    @Resource
    private ISysOrderService orderService;

    @RabbitListener(
            queues = "inventory-compensation-queue.order-services",
            ackMode = "MANUAL"
    )
    public void handleInventoryFailed(
            @Payload String messageBody,
            @Header("routingKey") String routingKey,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            if ("inventory.failed".equals(routingKey)) {
                // 幂等性检查
                if (!redisCacheUtil.setIfAbsent(ORDER_INVENTORY_PROCESSED.formatted(messageId), PROCESSED, 60)) {
                    log.warn("order服务重复消息: {}", messageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                processInventoryFailed(messageBody);
                channel.basicAck(deliveryTag, false);
            }
        } catch (Exception e) {
            log.error("库存扣减失败补偿处理失败: {}", messageBody, e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("消息拒绝失败: {}", ex.getMessage());
            }
        }
    }

    /**
     * 处理库存扣减失败补偿
     *
     * @param messageBody 消息体
     */
    @Transactional(rollbackFor = Exception.class)
    private void processInventoryFailed(String messageBody) {
        InventoryFailedEvent failedEvent = JSON.parseObject(messageBody, InventoryFailedEvent.class);
        log.info("订单服务处理库存扣减失败补偿: {}", messageBody);
        
        try {
            String orderId = failedEvent.getOrderId();
            
            // 更新订单状态为已取消
            updateOrderStatus(orderId, KnetOrderGroupStatus.CANCELLED);
            
            log.info("库存扣减失败补偿处理完成，订单状态已更新为已取消: orderId={}, reason={}", 
                    orderId, failedEvent.getFailureReason());
            
        } catch (Exception e) {
            log.error("处理库存扣减失败补偿异常: orderId={}, error={}", failedEvent.getOrderId(), e.getMessage());
            throw new ServiceException("处理库存扣减失败补偿异常: " + e.getMessage());
        }
    }
    
    /**
     * 更新订单状态
     *
     * @param orderId 订单ID
     * @param status 新状态
     */
    private void updateOrderStatus(String orderId, KnetOrderGroupStatus status) {
        // 更新父订单状态
        boolean groupUpdated = orderGroupService.updateOrderStatus(orderId, status);
        if (!groupUpdated) {
            throw new ServiceException("更新父订单状态失败: " + orderId);
        }
        
        // 更新子订单状态
        boolean orderUpdated = orderService.updateOrderStatusByParentId(orderId, status);
        if (!orderUpdated) {
            throw new ServiceException("更新子订单状态失败: " + orderId);
        }
        
        log.info("订单状态更新成功: orderId={}, status={}", orderId, status.getName());
    }
}
