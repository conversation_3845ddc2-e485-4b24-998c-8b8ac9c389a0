package com.knet.order.mq.consumer;

import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.service.ISysOrderProcessService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

import static com.knet.common.constants.SystemConstant.ORDER_INVENTORY_PROCESSED;
import static com.knet.common.constants.SystemConstant.PROCESSED;

/**
 * <AUTHOR>
 * @date 2025/6/11 16:25
 * @description: 库存扣减失败补偿事件消费者
 */
@Slf4j
@Component
public class InventoryFailedConsumer {

    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private ISysOrderProcessService orderProcessService;

    @RabbitListener(
            queues = "inventory-compensation-queue.order-services",
            ackMode = "MANUAL"
    )
    public void handleInventoryFailed(
            @Payload String messageBody,
            @Header("routingKey") String routingKey,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            if ("inventory.failed".equals(routingKey)) {
                // 幂等性检查
                if (!redisCacheUtil.setIfAbsent(ORDER_INVENTORY_PROCESSED.formatted(messageId), PROCESSED, 60)) {
                    log.warn("order服务重复消息: {}", messageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                orderProcessService.processInventoryFailed(messageBody);
                channel.basicAck(deliveryTag, false);
            }
        } catch (Exception e) {
            log.error("库存扣减失败补偿处理失败: {}", messageBody, e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("消息拒绝失败: {}", ex.getMessage());
            }
        }
    }
}
