package com.knet.order.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.enums.KnetPaymentFlowStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderService;
import com.knet.order.system.event.PaymentResultEvent;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

import static com.knet.common.constants.SystemConstant.ORDER_PAYMENT_PROCESSED;
import static com.knet.common.constants.SystemConstant.PROCESSED;

/**
 * <AUTHOR>
 * @date 2025/6/11 15:30
 * @description: 支付结果消息消费者
 */
@Slf4j
@Component
public class PaymentResultConsumer {
    
    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private ISysOrderGroupService orderGroupService;
    @Resource
    private ISysOrderService orderService;

    @RabbitListener(
            queues = "payment-result-queue.order-services",
            ackMode = "MANUAL"
    )
    public void handlePaymentResult(
            @Payload String messageBody,
            @Header("routingKey") String routingKey,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            if ("payment.result".equals(routingKey)) {
                // 幂等性检查
                if (!redisCacheUtil.setIfAbsent(ORDER_PAYMENT_PROCESSED.formatted(messageId), PROCESSED, 60)) {
                    log.warn("order服务重复消息: {}", messageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                processPaymentResult(messageBody);
                channel.basicAck(deliveryTag, false);
            }
        } catch (Exception e) {
            log.error("支付结果处理失败: {}", messageBody, e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("消息拒绝失败: {}", ex.getMessage());
            }
        }
    }

    /**
     * 处理支付结果
     *
     * @param messageBody 消息体
     */
    private void processPaymentResult(String messageBody) {
        PaymentResultEvent paymentResult = JSON.parseObject(messageBody, PaymentResultEvent.class);
        log.info("订单服务处理支付结果消息: {}", messageBody);
        
        try {
            String orderId = paymentResult.getOrderId();
            String paymentStatus = paymentResult.getStatus();
            
            // 根据支付状态更新订单状态
            if (KnetPaymentFlowStatus.SUCCESS.getName().equals(paymentStatus)) {
                // 支付成功，更新订单状态为已支付
                updateOrderStatus(orderId, KnetOrderGroupStatus.PAID);
                log.info("订单支付成功，订单状态已更新: orderId={}", orderId);
                
            } else if (KnetPaymentFlowStatus.FAILED.getName().equals(paymentStatus)) {
                // 支付失败，更新订单状态为已取消
                updateOrderStatus(orderId, KnetOrderGroupStatus.CANCELLED);
                log.info("订单支付失败，订单状态已更新为已取消: orderId={}", orderId);
                
            } else if (KnetPaymentFlowStatus.IN_PROGRESS.getName().equals(paymentStatus)) {
                // 支付中，更新订单状态为支付中
                updateOrderStatus(orderId, KnetOrderGroupStatus.IN_PAYMENT);
                log.info("订单支付中，订单状态已更新: orderId={}", orderId);
            }
            
        } catch (Exception e) {
            log.error("处理支付结果失败: orderId={}, error={}", paymentResult.getOrderId(), e.getMessage());
            throw new ServiceException("处理支付结果失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新订单状态
     *
     * @param orderId 订单ID
     * @param status 新状态
     */
    private void updateOrderStatus(String orderId, KnetOrderGroupStatus status) {
        // 更新父订单状态
        boolean groupUpdated = orderGroupService.updateOrderStatus(orderId, status);
        if (!groupUpdated) {
            throw new ServiceException("更新父订单状态失败: " + orderId);
        }
        
        // 更新子订单状态
        boolean orderUpdated = orderService.updateOrderStatusByParentId(orderId, status);
        if (!orderUpdated) {
            throw new ServiceException("更新子订单状态失败: " + orderId);
        }
        
        log.info("订单状态更新成功: orderId={}, status={}", orderId, status.getName());
    }
}
