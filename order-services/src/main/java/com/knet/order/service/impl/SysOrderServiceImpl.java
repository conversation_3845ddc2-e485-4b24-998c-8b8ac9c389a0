package com.knet.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.utils.RandomStrUtil;
import com.knet.order.mapper.SysOrderMapper;
import com.knet.order.model.dto.OrderItemDataDto;
import com.knet.order.model.entity.SysOrder;
import com.knet.order.service.ISysOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3 17:35
 * @description: 针对表【sys_order(B2B子订单表)】的数据库操作Service实现
 */
@Slf4j
@Service
public class SysOrderServiceImpl extends ServiceImpl<SysOrderMapper, SysOrder> implements ISysOrderService {

    @Resource
    private RandomStrUtil randomStrUtil;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysOrder createSysOrder(String parentOrderId, Long userId, String sku, List<OrderItemDataDto> itemDataList) {
        String subOrderId = randomStrUtil.getSubOrderId();
        BigDecimal totalAmount = calculateProductTotalAmount(itemDataList);
        Integer totalQuantity = calculateProductTotalQuantity(itemDataList);
        SysOrder subOrder = SysOrder.createSysOrder(
                parentOrderId, userId, subOrderId, sku, itemDataList.get(0).getProductName(), itemDataList.get(0).getImageUrl(), totalAmount, totalQuantity);
        this.save(subOrder);
        return subOrder;
    }

    /**
     * 计算单个商品的总金额
     */
    private static BigDecimal calculateProductTotalAmount(List<OrderItemDataDto> itemDataList) {
        return itemDataList
                .stream()
                .map(item -> item.getUnitPrice().multiply(new BigDecimal(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算单个商品的总数量
     */
    private static Integer calculateProductTotalQuantity(List<OrderItemDataDto> itemDataList) {
        return itemDataList
                .stream()
                .mapToInt(OrderItemDataDto::getQuantity)
                .sum();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderStatusByParentId(String parentOrderId, KnetOrderGroupStatus status) {
        log.info("更新子订单状态: parentOrderId={}, status={}", parentOrderId, status.getName());
        try {
            LambdaUpdateWrapper<SysOrder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(SysOrder::getParentOrderId, parentOrderId)
                    .set(SysOrder::getStatus, status);
            boolean updated = this.update(updateWrapper);
            log.info("子订单状态更新结果: parentOrderId={}, status={}, result={}", parentOrderId, status.getName(), updated);
            return updated;
        } catch (Exception e) {
            log.error("更新子订单状态失败: parentOrderId={}, status={}, error={}", parentOrderId, status.getName(), e.getMessage(), e);
            return false;
        }
    }
}
