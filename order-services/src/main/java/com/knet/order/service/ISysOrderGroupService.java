package com.knet.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.order.model.dto.OrderItemDataDto;
import com.knet.order.model.entity.SysOrderGroup;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order(B2B订单主表)】的数据库操作Service
 */
public interface ISysOrderGroupService extends IService<SysOrderGroup> {


    /**
     * 创建订单主表记录
     *
     * @param userId          用户ID
     * @param productGroupMap 商品分组数据
     * @param addressId       订单地址
     * @return 订单主表记录
     */
    SysOrderGroup createOrderGroup(Long userId, Map<String, List<OrderItemDataDto>> productGroupMap, Long addressId);

    /**
     * 根据订单号获取订单主表记录
     *
     * @param orderId 订单号
     * @return 订单主表记录
     */
    SysOrderGroup getOrderGroupByOrderId(String orderId);

    /**
     * 更新订单状态
     *
     * @param orderId 订单ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateOrderStatus(String orderId, KnetOrderGroupStatus status);
}
