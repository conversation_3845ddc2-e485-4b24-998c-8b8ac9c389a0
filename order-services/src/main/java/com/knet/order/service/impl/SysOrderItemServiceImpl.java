package com.knet.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.order.mapper.SysOrderItemMapper;
import com.knet.order.model.dto.OrderItemDataDto;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.service.ISysOrderItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order_item(订单商品明细)】的数据库操作Service实现
 */
@Slf4j
@Service
public class SysOrderItemServiceImpl extends ServiceImpl<SysOrderItemMapper, SysOrderItem> implements ISysOrderItemService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<SysOrderItem> createOrderItems(String subOrderId, List<OrderItemDataDto> orderItemDataList, String parentOrderId) {
        List<SysOrderItem> orderItems = new ArrayList<>();
        for (OrderItemDataDto itemData : orderItemDataList) {
            // 根据数量创建多条记录，每条记录的count都是1
            for (int i = 0; i < itemData.getQuantity(); i++) {
                SysOrderItem orderItem = SysOrderItem.createSysOrderItem(subOrderId, parentOrderId, itemData);
                orderItems.add(orderItem);
            }
        }
        this.saveBatch(orderItems);
        return orderItems;
    }

    @Override
    public List<SysOrderItem> getOrderItemsByPrentOrderId(String prentOrderId) {
        LambdaQueryWrapper<SysOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOrderItem::getParentOrderId, prentOrderId);
        return this.list(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateOrderStatusByParentId(String parentOrderId, KnetOrderItemStatus status) {
        log.info("更新item订单 状态: parentOrderId={}, status={}", parentOrderId, status.getName());
        try {
            LambdaUpdateWrapper<SysOrderItem> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(SysOrderItem::getParentOrderId, parentOrderId)
                    .set(SysOrderItem::getStatus, status);
            boolean updated = this.update(updateWrapper);
            log.info("更新item订单 状态更新结果: parentOrderId={}, status={}, result={}", parentOrderId, status.getName(), updated);
            return updated;
        } catch (Exception e) {
            log.error("更新item订单 状态失败: parentOrderId={}, status={}, error={}", parentOrderId, status.getName(), e.getMessage(), e);
            return false;
        }
    }
}
